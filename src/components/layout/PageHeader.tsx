'use client';

import { cn } from '@/lib/utils';
import { EnhancedThemeToggle } from '@/components/ui/enhanced-theme-toggle';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { ProgressSteps } from '@/components/ui/progress-steps';
import { ExportMenu } from '@/features/export/components';
import type { GPXData } from '@/types';

interface Step {
  label: string;
  description: string;
  status: 'complete' | 'in-progress' | 'pending';
  icon: React.ReactNode;
}

interface PageHeaderProps {
  gpxData: GPXData | null;
  uploadSteps: Step[];
  activeStep: number;
  pdfExportProps: any;
}

export function PageHeader({ 
  gpxData, 
  uploadSteps, 
  activeStep, 
  pdfExportProps 
}: PageHeaderProps) {
  return (
    <div className="flex flex-col space-y-3 sm:space-y-4">
      <div className="flex flex-wrap justify-between items-center gap-2">
        {/* Hide breadcrumb on smallest screens */}
        <div className="hidden sm:block">
          <Breadcrumb
            segments={[
              { label: 'Home', href: '/' },
              { label: 'Route Planner', href: '#' },
            ]}
          />
        </div>
        {/* Show simplified title on smallest screens */}
        <div className="sm:hidden text-sm font-medium">
          Route Planner
        </div>
        <div className="flex items-center gap-1.5 sm:gap-2">
          <EnhancedThemeToggle />
          <ExportMenu {...pdfExportProps} />
        </div>
      </div>

      {gpxData && (
        <ProgressSteps
          steps={uploadSteps}
          activeStep={activeStep}
          showStepNumbers={false}
          showDescriptions={false}
          data-show-descriptions-on-mobile={false}
          data-show-labels-on-mobile={true}
          className="mt-2"
        />
      )}
    </div>
  );
}
