'use client';

import { cn } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import MapWrapper from '@/components/map/MapWrapper';
import { ModernClientTimeline } from '@/components/timeline/ModernClientTimeline';
import { Alerts as WeatherAlerts } from '@/features/weather/components';
import { ModernTripSummary as RouteSummary } from '@/features/route/components';
import { UserGuide } from '@/features/help/components';
import { KeyboardNavigation } from '@/features/navigation/components';
import ClientSideCharts from '@/components/charts/ClientSideCharts';
import type { GPXData, ForecastPoint, WeatherData } from '@/types';

interface MainContentProps {
  isLoading: boolean;
  loadingMessage: string;
  gpxData: GPXData | null;
  forecastPoints: ForecastPoint[];
  weatherData: WeatherData[];
  selectedMarker: number | null;
  mapRef: React.RefObject<HTMLDivElement>;
  chartsRef: React.RefObject<HTMLDivElement>;
  onMarkerClick: (index: number) => void;
  onTimelineClick: (index: number) => void;
  onChartClick: (index: number) => void;
}

export function MainContent({
  isLoading,
  loadingMessage,
  gpxData,
  forecastPoints,
  weatherData,
  selectedMarker,
  mapRef,
  chartsRef,
  onMarkerClick,
  onTimelineClick,
  onChartClick,
}: MainContentProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[320px] sm:h-[350px] md:h-[450px] bg-card rounded-lg border border-border shadow-sm animate-scale-up">
        <LoadingSpinner
          message={loadingMessage || 'Loading weather data...'}
          centered
          variant="train"
          withContainer
          size="lg"
        />
      </div>
    );
  }

  return (
    <>
      {/* Map - most important visual element */}
      <div className="relative" ref={mapRef}>
        <div className="h-[320px] sm:h-[350px] md:h-[450px] rounded-lg overflow-hidden border border-border shadow-sm animate-scale-up">
          <MapWrapper
            gpxData={gpxData}
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            onMarkerClick={onMarkerClick}
            selectedMarker={selectedMarker}
          />
        </div>

        {forecastPoints.length > 0 && (
          <KeyboardNavigation
            onNavigate={direction => console.log(`Navigate ${direction}`)}
            onZoom={direction => console.log(`Zoom ${direction}`)}
            onSelectMarker={index => index !== null && onMarkerClick(index)}
            markerCount={forecastPoints.length}
          />
        )}
      </div>

      {forecastPoints.length > 0 && weatherData.length > 0 && (
        <div className={cn('space-y-3 sm:space-y-4 md:space-y-6 mt-3 sm:mt-4 md:mt-6 animate-slide-up')}>
          {/* Summary and alerts - stacked on mobile, side by side on larger screens */}
          <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4')}>
            <div className="w-full overflow-hidden rounded-xl">
              <RouteSummary
                gpxData={gpxData}
                forecastPoints={forecastPoints}
                weatherData={weatherData}
                className="animate-fade-in stagger-item"
              />
            </div>

            <WeatherAlerts
              forecastPoints={forecastPoints}
              weatherData={weatherData}
              maxInitialAlerts={3}
              compact={true}
              className="animate-fade-in stagger-item rounded-lg shadow-sm hover:shadow-md transition-all duration-300 card-hover-effect"
            />
          </div>

          {/* Modern iOS 19-style Timeline */}
          <div className="w-full overflow-hidden rounded-xl border border-border/20">
            <ModernClientTimeline
              forecastPoints={forecastPoints}
              weatherData={weatherData}
              selectedMarker={selectedMarker}
              onTimelineClick={onTimelineClick}
              height="h-[420px] sm:h-[440px] md:h-[460px] lg:h-[480px]"
              showNavigation={true}
              className="border-0"
            />
          </div>

          {/* Charts - adjusted padding and height for mobile */}
          <div
            ref={chartsRef}
            className="mb-4 sm:mb-6 md:mb-8 w-full overflow-hidden rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-border/20 bg-white/30 dark:bg-card/30 backdrop-blur-sm p-2 sm:p-3 animate-fade-in stagger-item"
          >
            <div className="w-full overflow-hidden rounded-xl">
              <ClientSideCharts
                gpxData={gpxData}
                forecastPoints={forecastPoints}
                weatherData={weatherData}
                selectedMarker={selectedMarker}
                onChartClick={onChartClick}
                height="h-[420px] sm:h-[450px] md:h-[480px] lg:h-[500px]"
                className="border-0"
              />
            </div>
          </div>
        </div>
      )}

      {/* User guide - moved to the bottom as it's less critical */}
      <div className={cn('mt-3 sm:mt-4 md:mt-6')}>
        <UserGuide
          className={cn(
            'animate-fade-in stagger-item rounded-lg shadow-sm hover:shadow-md transition-all duration-300 card-hover-effect'
          )}
        />
      </div>
    </>
  );
}
