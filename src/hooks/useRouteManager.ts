'use client';

import { useRef } from 'react';
import { Upload, CloudRain, Bar<PERSON>hart } from 'lucide-react';
import { Map as MapIcon } from 'lucide-react';
import gsap from 'gsap';

import { useWeather } from '@/features/weather/context';
import { useNotifications } from '@/features/notifications/context';
import type { GPXData, RouteSettings } from '@/types';

export function useRouteManager() {
  const {
    gpxData,
    forecastPoints,
    weatherData,
    selectedMarker,
    setGpxData,
    setSelectedMarker,
    generateWeatherForecast,
    isLoading,
    isGenerating,
    loadingMessage,
  } = useWeather();

  const { addNotification } = useNotifications();

  // Refs for PDF export
  const mapRef = useRef<HTMLDivElement>(null);
  const chartsRef = useRef<HTMLDivElement>(null);

  // PDF export component props
  const pdfExportProps = {
    gpxData,
    forecastPoints,
    weatherData,
    mapRef: mapRef as React.RefObject<HTMLDivElement>,
    chartsRef: chartsRef as React.RefObject<HTMLDivElement>,
  };

  // Handle GPX file upload
  const handleGPXLoaded = (data: GPXData) => {
    console.log('handleGPXLoaded called with data:', data.name, `${data.points.length} points`);

    setGpxData(data);
    console.log('setGpxData completed');

    setSelectedMarker(null);
    console.log('setSelectedMarker completed');

    // Show success notification
    addNotification(
      'success',
      `Route loaded successfully: ${data.name || 'Unnamed route'} (${data.points.length} points)`
    );
    console.log('Success notification added');

    // Wait for component to render before animating
    setTimeout(() => {
      const mapContainer = document.querySelector('.map-container');
      if (mapContainer) {
        gsap.fromTo(
          '.map-container',
          { y: 50, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.5, ease: 'power2.out' }
        );
      }
    }, 0);
  };

  // Handle route settings update
  const handleUpdateSettings = async (settings: RouteSettings) => {
    console.log('handleUpdateSettings called with:', settings);
    // Use the generateWeatherForecast function from context
    try {
      await generateWeatherForecast(
        settings.weatherInterval,
        settings.startTime,
        settings.avgSpeed
      );
      console.log('Weather forecast generated successfully');
    } catch (error) {
      console.error('Error generating weather forecast:', error);
      addNotification('error', 'Failed to generate weather forecast');
    }
  };

  // Handle marker click on map
  const handleMarkerClick = (index: number) => {
    setSelectedMarker(index);
  };

  // Handle timeline click
  const handleTimelineClick = (index: number) => {
    setSelectedMarker(index);
  };

  // Handle chart click
  const handleChartClick = (index: number) => {
    setSelectedMarker(index);
  };

  // Define the processing steps for GPX upload
  const uploadSteps = [
    {
      label: 'Upload GPX',
      description: 'Select and upload a GPX file',
      status: gpxData ? 'complete' : 'pending',
      icon: <Upload className="h-3 w-3" />,
    },
    {
      label: 'Process Route',
      description: 'Extract route data from GPX',
      status: gpxData ? 'complete' : 'pending',
      icon: <MapIcon className="h-3 w-3" />,
    },
    {
      label: 'Get Weather',
      description: 'Fetch weather data for route points',
      status: isGenerating ? 'in-progress' : weatherData.length > 0 ? 'complete' : 'pending',
      icon: <CloudRain className="h-3 w-3" />,
    },
    {
      label: 'Visualize',
      description: 'Display route with weather data',
      status: forecastPoints.length > 0 && weatherData.length > 0 ? 'complete' : 'pending',
      icon: <BarChart className="h-3 w-3" />,
    },
  ] as const;

  // Determine the active step
  const getActiveStep = () => {
    if (!gpxData) return 0;
    if (gpxData && !weatherData.length) return 1;
    if (isGenerating) return 2;
    if (forecastPoints.length > 0 && weatherData.length > 0) return 3;
    return 0;
  };

  return {
    // State
    gpxData,
    forecastPoints,
    weatherData,
    selectedMarker,
    isLoading,
    isGenerating,
    loadingMessage,
    
    // Refs
    mapRef,
    chartsRef,
    
    // Computed values
    uploadSteps,
    activeStep: getActiveStep(),
    pdfExportProps,
    
    // Handlers
    handleGPXLoaded,
    handleUpdateSettings,
    handleMarkerClick,
    handleTimelineClick,
    handleChartClick,
  };
}
